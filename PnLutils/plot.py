#!/usr/bin/env python3
import re
import sys
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path

def parse_log(log_path: Path) -> pd.DataFrame:
    epoch_re = re.compile(
        r"\[Epoch\s+(\d+)\]\s+Train:\s+loss=([0-9.]+)\s+\|\s+ic=([\-0-9.]+)\s+Valid:\s+loss=([0-9.]+)\s+\|\s+ic=([\-0-9.]+)"
    )
    fold_start_re = re.compile(r"\[Fold\s+(\d+)\]\s*开始训练")
    records = []
    current_fold = None
    with log_path.open('r', encoding='utf-8', errors='ignore') as f:
        for line in f:
            m_fold = fold_start_re.search(line)
            if m_fold:
                current_fold = int(m_fold.group(1))
                continue
            m_epoch = epoch_re.search(line)
            if m_epoch and current_fold is not None:
                ep = int(m_epoch.group(1))
                tr_loss = float(m_epoch.group(2))
                tr_ic = float(m_epoch.group(3))
                va_loss = float(m_epoch.group(4))
                va_ic = float(m_epoch.group(5))
                records.append({
                    'fold': current_fold, 'epoch': ep,
                    'train_loss': tr_loss, 'train_ic': tr_ic,
                    'valid_loss': va_loss, 'valid_ic': va_ic
                })
    df = pd.DataFrame.from_records(records).sort_values(['fold','epoch']).reset_index(drop=True)
    return df

def plot_2x2(df: pd.DataFrame, out_path: Path, title_name: str = ''):
    fig, axes = plt.subplots(2, 2, figsize=(18, 12))
    (ax_train_loss, ax_valid_loss), (ax_train_ic, ax_valid_ic) = axes

    # Train Loss
    for fold, g in df.groupby('fold'):
        g = g.sort_values('epoch')
        ax_train_loss.plot(g['epoch'], g['train_loss'], label=f'Fold {fold}')
    ax_train_loss.set_title('Train Loss vs. Epoch')
    ax_train_loss.set_xlabel('Epoch'); ax_train_loss.set_ylabel('Loss')
    ax_train_loss.grid(True, linestyle='--', linewidth=0.5, alpha=0.7)
    ax_train_loss.legend(ncol=3, fontsize=8, title='Folds')

    # Valid Loss (mark best min)
    for fold, g in df.groupby('fold'):
        g = g.sort_values('epoch')
        ax_valid_loss.plot(g['epoch'], g['valid_loss'], label=f'Fold {fold}')
        idx = g['valid_loss'].idxmin(); row = g.loc[idx]
        ax_valid_loss.scatter(row['epoch'], row['valid_loss'], s=30, zorder=5)
        ax_valid_loss.annotate(f'F{fold}@{int(row["epoch"])}', (row['epoch'], row['valid_loss']),
                               textcoords='offset points', xytext=(6, -10), fontsize=8)
    ax_valid_loss.set_title('Valid Loss vs. Epoch (Best marked)')
    ax_valid_loss.set_xlabel('Epoch'); ax_valid_loss.set_ylabel('Loss')
    ax_valid_loss.grid(True, linestyle='--', linewidth=0.5, alpha=0.7)
    ax_valid_loss.legend(ncol=3, fontsize=8, title='Folds')

    # Train IC
    for fold, g in df.groupby('fold'):
        g = g.sort_values('epoch')
        ax_train_ic.plot(g['epoch'], g['train_ic'], label=f'Fold {fold}')
    ax_train_ic.set_title('Train IC vs. Epoch')
    ax_train_ic.set_xlabel('Epoch'); ax_train_ic.set_ylabel('IC')
    ax_train_ic.grid(True, linestyle='--', linewidth=0.5, alpha=0.7)
    ax_train_ic.legend(ncol=3, fontsize=8, title='Folds')

    # Valid IC (mark best max)
    for fold, g in df.groupby('fold'):
        g = g.sort_values('epoch')
        ax_valid_ic.plot(g['epoch'], g['valid_ic'], label=f'Fold {fold}')
        idx = g['valid_ic'].idxmax(); row = g.loc[idx]
        ax_valid_ic.scatter(row['epoch'], row['valid_ic'], s=30, zorder=5)
        ax_valid_ic.annotate(f'F{fold}@{int(row["epoch"])}', (row['epoch'], row['valid_ic']),
                             textcoords='offset points', xytext=(6, -10), fontsize=8)
    ax_valid_ic.set_title('Valid IC vs. Epoch (Best marked)')
    ax_valid_ic.set_xlabel('Epoch'); ax_valid_ic.set_ylabel('IC')
    ax_valid_ic.grid(True, linestyle='--', linewidth=0.5, alpha=0.7)
    ax_valid_ic.legend(ncol=3, fontsize=8, title='Folds')

    fig.suptitle(f'{title_name}  Training Curves by Fold (2x2)', fontsize=16)
    fig.tight_layout(rect=[0, 0.02, 1, 0.98])
    fig.savefig(out_path, dpi=180)

def main():
    if len(sys.argv) < 2:
        print("Usage: plot_train_log_2x2.py /path/to/logfile.log")
        sys.exit(1)
    log_path = Path(sys.argv[1])
    df = parse_log(log_path)
    # save parsed metrics
    # df.to_csv(log_path.with_suffix('.metrics.csv'), index=False)
    # render 2x2
    out_path = log_path.with_suffix('.2x2.png')
    plot_2x2(df, out_path, title_name=log_path)
    print('Saved:', out_path)

if __name__ == '__main__':
    main()