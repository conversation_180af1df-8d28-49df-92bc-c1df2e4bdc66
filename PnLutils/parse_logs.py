#!/usr/bin/env python3
"""
Parse .log files in the logs directory to extract fold data from wandb Run summary,
generate markdown tables and visualization charts.

fold_0 corresponds to 2018
fold_1 corresponds to 2019
...
fold_6 corresponds to 2024
"""

import os
import re
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

def parse_log_file(log_path):
    """
    Parse a single log file to extract fold data

    Args:
        log_path: Path to the log file

    Returns:
        dict: Contains fold_0 to fold_6 data, returns None if not found
    """
    try:
        with open(log_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Find content after "wandb: Run summary:"
        summary_match = re.search(r'wandb: Run summary:(.*?)(?=\n\n|\Z)', content, re.DOTALL)
        if not summary_match:
            print(f"Warning: 'wandb: Run summary:' not found in {log_path}")
            return None

        summary_content = summary_match.group(1)

        # Extract fold data
        fold_data = {}
        for i in range(7):  # fold_0 to fold_6
            fold_pattern = rf'wandb:\s+fold_{i}\s+([\d.]+)'
            fold_match = re.search(fold_pattern, summary_content)
            if fold_match:
                fold_data[f'fold_{i}'] = float(fold_match.group(1))
            else:
                print(f"Warning: fold_{i} not found in {log_path}")
                fold_data[f'fold_{i}'] = None

        return fold_data

    except Exception as e:
        print(f"Error: Failed to parse file {log_path}: {e}")
        return None

def collect_all_data(logs_dir):
    """
    Collect data from all log files

    Args:
        logs_dir: Path to logs directory

    Returns:
        dict: Mapping from model names to fold data
    """
    logs_path = Path(logs_dir)
    all_data = {}

    # Iterate through all .log files
    for log_file in logs_path.glob('*.log'):
        model_name = log_file.stem  # Remove .log extension
        print(f"Processing: {log_file}")

        fold_data = parse_log_file(log_file)
        if fold_data:
            all_data[model_name] = fold_data

    return all_data

def create_dataframe(all_data):
    """
    Convert data to DataFrame

    Args:
        all_data: Model data dictionary

    Returns:
        pd.DataFrame: Organized dataframe
    """
    # Year mapping
    year_mapping = {
        'fold_0': '2018',
        'fold_1': '2019',
        'fold_2': '2020',
        'fold_3': '2021',
        'fold_4': '2022',
        'fold_5': '2023',
        'fold_6': '2024'
    }

    # Create DataFrame
    rows = []
    for model_name, fold_data in all_data.items():
        row = {'Model': model_name}
        for fold, year in year_mapping.items():
            row[year] = fold_data.get(fold)
        rows.append(row)

    df = pd.DataFrame(rows)
    df = df.set_index('Model')

    return df

def save_markdown_table(df, output_path):
    """
    Save DataFrame as markdown table

    Args:
        df: DataFrame
        output_path: Output file path
    """
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write("# Model Performance Comparison Table\n\n")
        f.write("Performance of each model across different years (folds):\n\n")
        f.write(df.to_markdown(floatfmt='.5f'))
        f.write("\n\n")
        f.write("Note: fold_0 corresponds to 2018, fold_1 to 2019, and so on until fold_6 corresponds to 2024\n")

def create_visualizations(df, output_dir):
    """
    Create visualization charts

    Args:
        df: DataFrame
        output_dir: Output directory
    """
    # Set style and color palette
    sns.set_style("whitegrid")
    sns.set_palette("husl")
    plt.rcParams['font.size'] = 12
    plt.rcParams['axes.labelsize'] = 14
    plt.rcParams['axes.titlesize'] = 16
    plt.rcParams['legend.fontsize'] = 12

    # 1. Heatmap
    plt.figure(figsize=(12, 8))
    sns.heatmap(df, annot=True, cmap='viridis', fmt='.4f',
                cbar_kws={'label': 'Performance Score'},
                linewidths=0.5, linecolor='white')
    plt.title('Model Performance Heatmap (2018-2024)', fontsize=18, pad=20, fontweight='bold')
    plt.xlabel('Year', fontsize=14, fontweight='bold')
    plt.ylabel('Model', fontsize=14, fontweight='bold')
    plt.xticks(rotation=0)
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'performance_heatmap.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 2. Bar chart comparison by year
    fig, axes = plt.subplots(2, 4, figsize=(20, 12))
    axes = axes.flatten()

    # Color palette for models
    colors = sns.color_palette("Set2", len(df.index))
    model_colors = dict(zip(df.index, colors))

    for i, year in enumerate(df.columns):
        ax = axes[i]
        year_data = df[year].dropna()

        # Create bars with different colors for each model
        bars = ax.bar(range(len(year_data)), year_data.values,
                     color=[model_colors[model] for model in year_data.index],
                     alpha=0.8, edgecolor='black', linewidth=0.5)

        ax.set_title(f'{year} Model Performance', fontsize=14, fontweight='bold', pad=15)
        ax.set_ylabel('Performance Score', fontsize=12, fontweight='bold')
        ax.set_xticks(range(len(year_data)))
        ax.set_xticklabels(year_data.index, rotation=45, ha='right', fontsize=10)
        ax.grid(axis='y', alpha=0.3)

        # Add value labels on bars
        for bar, value in zip(bars, year_data.values):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.002,
                   f'{value:.4f}', ha='center', va='bottom', fontsize=9, fontweight='bold')

        # Set y-axis limits for better comparison
        ax.set_ylim(0, max(df.max()) * 1.1)

    # Hide the last subplot (since we only have 7 years)
    axes[7].set_visible(False)

    plt.suptitle('Model Performance Comparison by Year', fontsize=20, fontweight='bold', y=0.98)
    plt.tight_layout()
    plt.subplots_adjust(top=0.93)
    plt.savefig(os.path.join(output_dir, 'yearly_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """Main function"""
    # Set paths
    logs_dir = 'logs'
    output_dir = 'logs'

    print("Starting to parse log files...")

    # Collect data
    all_data = collect_all_data(logs_dir)

    if not all_data:
        print("Error: No valid data found")
        return

    print(f"Successfully parsed data from {len(all_data)} models")

    # Create DataFrame
    df = create_dataframe(all_data)
    print("\nData preview:")
    print(df)

    # Save markdown table
    markdown_path = os.path.join(output_dir, 'performance_table.md')
    save_markdown_table(df, markdown_path)
    print(f"\nMarkdown table saved to: {markdown_path}")

    # Create visualizations
    print("Generating visualization charts...")
    create_visualizations(df, output_dir)
    print("Visualization charts generated:")
    print(f"  - Heatmap: {os.path.join(output_dir, 'performance_heatmap.png')}")
    print(f"  - Yearly comparison: {os.path.join(output_dir, 'yearly_comparison.png')}")

    print("\nProcessing completed!")

if __name__ == "__main__":
    main()
