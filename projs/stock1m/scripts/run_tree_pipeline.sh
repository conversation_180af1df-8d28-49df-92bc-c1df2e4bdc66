#!/bin/bash

# Tree Pipeline 启动脚本
# 基于CPU的LightGBM树模型训练

set -e

# 默认配置
DATA_PATH=${DATA_PATH:-"projs/stock1m/data/OHLCVA_Vwap_cube.npy"}
N_TRIALS=${N_TRIALS:-50}
WANDB_MODE=${WANDB_MODE:-"online"}
EXP_NAME=${EXP_NAME:-"tree_$(date +%m%d_%H%M%S)"}
FORCE_OPTUNA=${FORCE_OPTUNA:-false}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --data_path)
            DATA_PATH="$2"
            shift 2
            ;;
        --n_trials)
            N_TRIALS="$2"
            shift 2
            ;;
        --wandb_mode)
            WANDB_MODE="$2"
            shift 2
            ;;
        --exp_name)
            EXP_NAME="$2"
            shift 2
            ;;
        --force_optuna)
            FORCE_OPTUNA=true
            shift 1
            ;;
        --help|-h)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --data_path PATH       数据文件路径, 默认: projs/stock1m/data/OHLCVA_Vwap_cube.npy"
            echo "  --n_trials NUM         Optuna搜参试验次数, 默认: 50"
            echo "  --wandb_mode MODE      W&B模式 (online|offline), 默认: online"
            echo "  --exp_name NAME        实验名称, 默认: tree_MMDD_HHMMSS"
            echo "  --force_optuna         强制进行Optuna搜索，否则使用预设最优参数"
            echo "  --help, -h             显示此帮助信息"
            echo ""
            echo "说明:"
            echo "  Tree Pipeline使用LightGBM在CPU上进行训练"
            echo "  支持超大数据的内存映射处理"
            echo "  使用Optuna进行超参数优化"
            echo "  自动进行滚动训练和测试"
            echo ""
            echo "示例:"
            echo "  $0 --n_trials 100 --wandb_mode offline"
            echo "  $0 --data_path /path/to/data.npy --exp_name my_tree_exp"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 验证数据文件
if [[ ! -f "$DATA_PATH" ]]; then
    echo "错误: 数据文件不存在: $DATA_PATH"
    exit 1
fi

# 设置环境变量
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 检查依赖包
echo "🔍 检查依赖包..."
python -c "import lightgbm, optuna, numpy, loguru" 2>/dev/null || {
    echo "❌ 缺少必要的依赖包，请安装："
    echo "   pip install lightgbm optuna loguru"
    exit 1
}

# 设置wandb token（如果存在）
if [[ -n "${WANDB_API_KEY}" ]]; then
    echo "✅ W&B API Key已设置"
else
    echo "⚠️  W&B API Key未设置，将使用离线模式"
    WANDB_MODE="offline"
fi

# 创建必要的目录
mkdir -p projs/stock1m/checkpoints_tree
mkdir -p projs/stock1m/work_tree
mkdir -p projs/stock1m/logs/wandb

# 启动训练
echo "🌳 开始启动 Tree Pipeline..."
echo "📊 数据路径: $DATA_PATH"
echo "🔬 搜参试验: $N_TRIALS"
echo "📝 W&B模式: $WANDB_MODE"
echo "🏷️  实验名称: $EXP_NAME"
if [[ "$FORCE_OPTUNA" == "true" ]]; then
    echo "🔍 强制进行Optuna超参搜索"
else
    echo "⚡ 使用预设最优参数 (跳过Optuna搜索)"
fi
echo "----------------------------------------"

# 构建命令参数
CMD_ARGS=(
    --data_path "$DATA_PATH"
    --n_trials "$N_TRIALS"
    --wandb_mode "$WANDB_MODE"
    --exp_name "$EXP_NAME"
)

# 如果强制开启optuna，添加到参数中
if [[ "$FORCE_OPTUNA" == "true" ]]; then
    CMD_ARGS+=(--force_optuna)
fi

python -m projs.stock1m.scripts.tree_pipeline "${CMD_ARGS[@]}"

TRAIN_EXIT_CODE=$?

if [ $TRAIN_EXIT_CODE -eq 0 ]; then
    echo "✅ Tree Pipeline执行成功完成"
    echo "📁 检查点保存在: projs/stock1m/checkpoints_tree/"
    echo "📊 工作文件保存在: projs/stock1m/work_tree/"
    echo "💡 可以删除work_tree目录以释放磁盘空间"
else
    echo "❌ Tree Pipeline执行失败，退出码: $TRAIN_EXIT_CODE"
fi

echo "Tree Pipeline脚本结束"
exit $TRAIN_EXIT_CODE