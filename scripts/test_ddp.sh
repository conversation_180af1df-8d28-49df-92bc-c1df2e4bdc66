#!/bin/bash

# DDP 测试启动脚本

echo "=== DDP GPU 测试脚本 ==="

# 检查 GPU 数量
GPU_COUNT=$(nvidia-smi --list-gpus | wc -l)
echo "检测到 $GPU_COUNT 个 GPU"

if [ $GPU_COUNT -eq 0 ]; then
    echo "❌ 未检测到 GPU，无法进行 DDP 测试"
    exit 1
fi

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7  # 根据实际GPU数量调整
export NCCL_DEBUG=WARN  # 只显示警告和错误
export NCCL_IGNORE_DISABLED_P2P=1  # 忽略 P2P 禁用信息

# 根据 GPU 数量选择进程数
if [ $GPU_COUNT -ge 8 ]; then
    NPROC=8
    echo "使用 8 卡进行测试"
elif [ $GPU_COUNT -ge 4 ]; then
    NPROC=4
    echo "使用 4 卡进行测试"
elif [ $GPU_COUNT -ge 2 ]; then
    NPROC=2
    echo "使用 2 卡进行测试"
else
    NPROC=1
    echo "使用 1 卡进行测试"
fi

# 切换到项目根目录
cd "$(dirname "$0")/.."

echo "当前目录: $(pwd)"
echo "启动 DDP 测试..."

# 启动 DDP 训练
torchrun \
    --nproc_per_node=$NPROC \
    --master_port=29500 \
    examples/test_ddp_gpu.py

echo "DDP 测试完成"
