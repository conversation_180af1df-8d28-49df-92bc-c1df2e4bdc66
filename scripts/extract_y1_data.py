#!/usr/bin/env python3
"""
提取y1数据脚本

从 OHLCVA_Vwap_y1_cube.npy 中提取最后一维 [-1, :, :, :] 
并保存为 y1.npy，形状为 [1, D, T, N]
"""

import os
import numpy as np
from loguru import logger

def extract_y1_data():
    """提取y1数据"""
    
    # 输入和输出路径
    input_path = "/home/<USER>/tslib/OHLCVA_Vwap_y1_cube.npy"
    output_path = "/disk4/shared/intern/laiyc/forModel/y1.npy"
    
    logger.info("=== 提取Y1数据 ===")
    logger.info(f"输入文件: {input_path}")
    logger.info(f"输出文件: {output_path}")
    
    # 检查输入文件
    if not os.path.exists(input_path):
        raise FileNotFoundError(f"输入文件不存在: {input_path}")
    
    # 创建输出目录
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 加载数据（使用mmap模式查看形状）
    logger.info("正在加载数据...")
    y_data = np.load(input_path, mmap_mode="r")
    F, D, T, N = y_data.shape
    
    logger.info(f"原始数据形状: F={F}, D={D}, T={T}, N={N}")
    logger.info(f"提取最后一维: [-1, :, :, :] -> [1, {D}, {T}, {N}]")
    
    # 提取最后一维并保存
    # 注意：这里会加载数据到内存，但只是最后一维
    logger.info("正在提取最后一维...")
    y1_data = y_data[-1:, :, :, :]  # 保持4D形状 [1, D, T, N]
    
    logger.info(f"提取后形状: {y1_data.shape}")
    
    # 保存数据
    logger.info("正在保存数据...")
    np.save(output_path, y1_data)
    
    # 验证保存的数据
    logger.info("验证保存的数据...")
    saved_data = np.load(output_path, mmap_mode="r")
    logger.info(f"保存的数据形状: {saved_data.shape}")
    
    # 计算文件大小
    file_size = os.path.getsize(output_path)
    file_size_gb = file_size / (1024**3)
    logger.info(f"输出文件大小: {file_size_gb:.2f} GB")
    
    logger.info("✓ Y1数据提取完成!")
    
    return output_path

if __name__ == "__main__":
    extract_y1_data()
