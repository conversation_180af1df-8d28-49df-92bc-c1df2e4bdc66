"""
通用通道拆分规格

规定最后一维通道排列：
[ 0 .. F_feat-1 | (可选) weight 通道 | 最后 F_label 个为 label ]
"""

from dataclasses import dataclass
from typing import Tuple
import numpy as np
import torch


@dataclass
class FieldSpec:
    """
    规定最后一维通道排列：
    [ 0 .. F_feat-1 | (可选) weight 通道 | 最后 F_label 个为 label ]
    例如：7 个特征 + 0~k 个权重 + 1~m 个标签
    """
    n_feat: int                 # 特征通道数
    n_label: int                # 标签通道数（>=1）
    n_weight: int = 0           # 权重通道数（可为 0）
    dtype: torch.dtype = torch.float32

    def split_np(self, x_np: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        按最后一维拆分为 (features, weight, label)
        兼容 x 的结尾维度为 F_total = n_feat + n_weight + n_label
        当 n_weight=0 时，返回全1权重
        """
        F_total = x_np.shape[-1]
        assert F_total >= self.n_feat + self.n_label + self.n_weight, \
            f"尾维 {F_total} < 期望 {self.n_feat}+{self.n_weight}+{self.n_label}"

        f = x_np[..., :self.n_feat]
        if self.n_weight > 0:
            w = x_np[..., self.n_feat:self.n_feat+self.n_weight]
        else:
            # 创建全1权重，形状与标签相同但最后一维为1
            label_shape = x_np[..., -self.n_label:].shape
            weight_shape = label_shape[:-1] + (1,)  # 替换最后一维为1
            w = np.ones(weight_shape, dtype=x_np.dtype)
        y = x_np[..., -self.n_label:]
        return f, w, y

    def to_tensor(self, *arrays: np.ndarray):
        """
        将 numpy 转为 torch
        """
        outs = []
        for arr in arrays:
            # 保证连续有利于 pin_memory / non_blocking
            arr = np.ascontiguousarray(arr)
            outs.append(torch.from_numpy(arr).to(self.dtype))
        return tuple(outs)
