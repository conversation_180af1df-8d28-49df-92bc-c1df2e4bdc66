"""
滚动切分生成器

提供按天或按年的滚动训练/验证/测试切分功能。
"""

from __future__ import annotations
from typing import Iterator, Tuple, Optional
import numpy as np


def rolling_splits_by_days(
    D: int,
    *,
    train_days: int,
    test_days: int,
    inner_val_ratio: float = 0.2,    # 训练窗口内最后 20% 做 valid
    step_days: Optional[int] = None, # 每次滚动步长（默认= test_days）
    drop_last_incomplete: bool = True,
) -> Iterator[Tuple[np.ndarray, np.ndarray, np.ndarray]]:
    """
    按天生成滚动切分
    
    参数:
        D: 总天数
        train_days: 训练窗口天数
        test_days: 测试窗口天数
        inner_val_ratio: 训练窗口内验证集比例
        step_days: 滚动步长，默认等于test_days
        drop_last_incomplete: 是否丢弃最后不完整的fold
    
    生成:
        (idx_train, idx_valid, idx_test): 训练、验证、测试索引
        
    示例:
        D=100, train_days=60, test_days=20, inner_val_ratio=0.2
        - Fold 0: train=[0:48], valid=[48:60], test=[60:80]
        - Fold 1: train=[20:68], valid=[68:80], test=[80:100]
    """
    if step_days is None:
        step_days = test_days
        
    start = 0
    while True:
        tr_beg = start
        tr_end = tr_beg + train_days
        te_end = tr_end + test_days
        
        # 检查训练窗口是否超出范围
        if tr_end > D:
            break
            
        # 检查测试窗口是否超出范围
        if te_end > D:
            if drop_last_incomplete:
                break
            te_end = D
            
        # 构造训练和验证索引
        idx_train_full = np.arange(tr_beg, tr_end, dtype=np.int64)
        n_tr = len(idx_train_full)
        n_val = max(1, int(round(n_tr * inner_val_ratio)))
        n_fit = n_tr - n_val
        
        idx_train = idx_train_full[:n_fit]
        idx_valid = idx_train_full[n_fit:]
        
        # 构造测试索引
        idx_test = np.arange(tr_end, te_end, dtype=np.int64)
        
        if len(idx_test) == 0:
            break
            
        yield idx_train, idx_valid, idx_test
        
        start += step_days


def rolling_splits_by_years(
    D: int,
    *,
    train_years: float,
    test_years: float,
    days_per_year: int = 252,  # 交易日
    inner_val_ratio: float = 0.2,
    step_years: Optional[float] = None,
    drop_last_incomplete: bool = True,
) -> Iterator[Tuple[np.ndarray, np.ndarray, np.ndarray]]:
    """
    按年生成滚动切分
    
    参数:
        D: 总天数
        train_years: 训练窗口年数
        test_years: 测试窗口年数
        days_per_year: 每年交易日数
        inner_val_ratio: 训练窗口内验证集比例
        step_years: 滚动步长年数，默认等于test_years
        drop_last_incomplete: 是否丢弃最后不完整的fold
    
    生成:
        (idx_train, idx_valid, idx_test): 训练、验证、测试索引
        
    示例:
        train_years=3, test_years=1, days_per_year=252
        相当于 train_days=756, test_days=252
    """
    if step_years is None:
        step_years = test_years
        
    return rolling_splits_by_days(
        D,
        train_days=int(round(train_years * days_per_year)),
        test_days=int(round(test_years * days_per_year)),
        step_days=int(round(step_years * days_per_year)),
        inner_val_ratio=inner_val_ratio,
        drop_last_incomplete=drop_last_incomplete,
    )


def get_fold_info(
    D: int,
    train_years: float,
    test_years: float,
    days_per_year: int = 252,
    **kwargs
) -> dict:
    """
    获取fold信息统计
    
    返回:
        包含fold数量、覆盖范围等信息的字典
    """
    folds = list(rolling_splits_by_years(
        D, 
        train_years=train_years, 
        test_years=test_years,
        days_per_year=days_per_year,
        **kwargs
    ))
    
    if not folds:
        return {"num_folds": 0, "coverage": 0.0}
    
    # 计算测试集覆盖的天数
    test_days_covered = set()
    for _, _, idx_test in folds:
        test_days_covered.update(idx_test)
    
    return {
        "num_folds": len(folds),
        "coverage": len(test_days_covered) / D,
        "train_days_per_fold": len(folds[0][0]) + len(folds[0][1]),
        "test_days_per_fold": len(folds[0][2]),
        "total_test_days": len(test_days_covered),
    }
