"""
CubeStandardizer - 数据标准化器（仅标准化 features，不动 weight/label）
"""

import numpy as np
import warnings
from typing import Optional
from .cube import MinuteCube
# 可选：避免循环依赖的话，不强依赖 FieldSpec 类型；只要求传入对象含 n_feat
# from .fields import FieldSpec

class CubeStandardizer:
    """
    标准化器：只做“拟合/应用标准化”，不做任何填充。

    mode:
      - 'global_f'   : 每个特征 f 一个 (mu, sd)，沿 (D,T,N) 计算（忽略 NaN）。
      - 'per_stock_f': 每个 (n,f) 一个 (mu, sd)，沿 (D,T) 计算（忽略 NaN）；
                       对“训练期未出现的新股票/占位股票”或 std 太小的条目，回退到全局 (mu_g, sd_g)。

    仅对前 n_feat 个通道进行标准化；中间的 weight 通道和末尾的 label 通道保持不变。

    属性：
      - stock_seen: [N]，训练期是否见过任意（feature）有效值
      - per_stock_seen: [N, n_feat]，训练期 (n,f) 是否见过有效值
      - mu, sd: 标准化统计，仅包含 features 通道（global: [n_feat]；per_stock: [N, n_feat]）
      - mu_g, sd_g: 全局统计，仅包含 features 通道（[n_feat]）
    """
    def __init__(self, mode: str = "per_stock_f", eps: float = 1e-6):
        assert mode in ("global_f", "per_stock_f")
        self.mode = mode
        self.eps = eps
        # 仅保存 features 的统计
        self.mu = None     # [n_feat] or [N, n_feat]
        self.sd = None     # [n_feat] or [N, n_feat]
        self.mu_g = None   # [n_feat]
        self.sd_g = None   # [n_feat]
        self.fitted = False
        self.stock_seen = None            # [N]
        self.per_stock_seen = None        # [N, n_feat]
        # 记录拟合时使用的 n_feat，transform 时校验一致
        self._fitted_n_feat: Optional[int] = None

    def _resolve_n_feat(self, cube: MinuteCube, spec=None, n_feat: Optional[int] = None) -> int:
        if spec is not None:
            n = int(spec.n_feat)
        elif n_feat is not None:
            n = int(n_feat)
        else:
            # 若未给出 spec/n_feat，默认全部通道视作 feature（不推荐）
            n = cube.F
        assert 1 <= n <= cube.F, f"n_feat={n} 超出范围 (1..{cube.F})"
        return n

    def fit(self, cube_train: MinuteCube, *, spec=None, n_feat: Optional[int] = None) -> "CubeStandardizer":
        """
        在训练数据上拟合标准化参数。
        只对前 n_feat 个通道做统计（features）。
        """
        X = cube_train.X_dtnf  # [D,T,N,F_total]
        D, T, N, F_total = cube_train.D, cube_train.T, cube_train.N, cube_train.F

        n_feat_resolved = self._resolve_n_feat(cube_train, spec=spec, n_feat=n_feat)
        X_feat = X[..., :n_feat_resolved]           # [D,T,N,n_feat]

        # --- 全局统计（features） ---
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", category=RuntimeWarning)
            mu_g = np.nanmean(X_feat, axis=(0, 1, 2))           # [n_feat]
            sd_g = np.nanstd (X_feat, axis=(0, 1, 2), ddof=0)   # [n_feat]

        mu_g = np.where(np.isfinite(mu_g), mu_g, 0.0).astype(np.float32)
        sd_g = np.where(np.isfinite(sd_g) & (sd_g >= self.eps), sd_g, 1.0).astype(np.float32)
        self.mu_g, self.sd_g = mu_g, sd_g

        if self.mode == "global_f":
            self.mu, self.sd = mu_g, sd_g
            # seen 信息（features）
            with warnings.catch_warnings():
                warnings.simplefilter("ignore", category=RuntimeWarning)
                per_stock_seen = np.isfinite(X_feat).any(axis=(0, 1))  # [N, n_feat]
                stock_seen = per_stock_seen.any(axis=1)                # [N]
            self.per_stock_seen = per_stock_seen
            self.stock_seen = stock_seen
            self._fitted_n_feat = n_feat_resolved
            self.fitted = True
            return self

        # --- per_stock_f：对每个 (n,f) 统计；未见/退化回退到全局 ---
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", category=RuntimeWarning)
            per_stock_seen = np.isfinite(X_feat).any(axis=(0, 1))  # [N, n_feat]
            stock_seen = per_stock_seen.any(axis=1)                # [N]
            mu_ps = np.nanmean(X_feat, axis=(0, 1))               # [N, n_feat]
            sd_ps = np.nanstd (X_feat, axis=(0, 1), ddof=0)       # [N, n_feat]

        invalid_mu = (~np.isfinite(mu_ps)) | (~per_stock_seen)
        invalid_sd = (~np.isfinite(sd_ps)) | (sd_ps < self.eps) | (~per_stock_seen)
        mu_ps = np.where(invalid_mu, mu_g[None, :], mu_ps)
        sd_ps = np.where(invalid_sd, sd_g[None, :], sd_ps)

        self.mu = mu_ps.astype(np.float32)   # [N, n_feat]
        self.sd = sd_ps.astype(np.float32)   # [N, n_feat]
        self.per_stock_seen = per_stock_seen
        self.stock_seen = stock_seen
        self._fitted_n_feat = n_feat_resolved
        self.fitted = True
        return self

    def transform(
        self,
        cube: MinuteCube,
        *,
        spec=None,
        n_feat: Optional[int] = None,
        out_dtype: np.dtype = np.float32,
        out_memmap_path: Optional[str] = None,
    ) -> MinuteCube:
        """
        应用标准化变换：只对 features（前 n_feat 个通道）做线性变换，其余通道（weight/label）保持不变。
        """
        assert self.fitted, "请先在训练集上 fit()"
        n_feat_resolved = self._resolve_n_feat(cube, spec=spec, n_feat=n_feat)
        if self._fitted_n_feat is not None:
            assert n_feat_resolved == self._fitted_n_feat, \
                f"transform 的 n_feat={n_feat_resolved} 与拟合时不一致（{self._fitted_n_feat}）"

        D, T, N, F_total = cube.D, cube.T, cube.N, cube.F
        shape = (D, T, N, F_total)

        # 申请输出（数组或 memmap）
        if out_memmap_path is None:
            X_out = np.empty(shape, dtype=out_dtype)
        else:
            X_out = np.memmap(out_memmap_path, mode="w+", dtype=out_dtype, shape=shape)

        # 先整体复制（weight/label 将被原样保留）
        np.copyto(X_out, cube.X_dtnf, casting="unsafe")

        # 只对 features 做标准化
        if self.mode == "global_f":
            # [D,T,N,n_feat] -= mu_g[None,None,None,:]；再除以 sd_g
            X_out[..., :n_feat_resolved] -= self.mu[None, None, None, :]
            X_out[..., :n_feat_resolved] /= self.sd[None, None, None, :]
        else:  # per_stock_f
            # [D,T,N,n_feat] -= mu_ps[None,None,:,:]；再除以 sd_ps
            X_out[..., :n_feat_resolved] -= self.mu[None, None, :, :]
            X_out[..., :n_feat_resolved] /= self.sd[None, None, :, :]

        # 返回新的 MinuteCube（构造器需要 [F,D,T,N]）
        return MinuteCube(np.moveaxis(X_out, (3, 0, 1, 2), (0, 1, 2, 3)))
