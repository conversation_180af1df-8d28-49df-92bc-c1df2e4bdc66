"""
MinuteCube - 核心数据容器

管理 [F, D, T, N] 格式的时间序列数据，提供视图转换和有效性掩码。
仅管理数据视图与有效性掩码，不做任何 pandas 操作。
"""

import numpy as np
from typing import Tuple, List


class MinuteCube:
    """
    仅管理数据视图与有效性掩码，不做任何 pandas 操作。
    输入: X [F, D, T, N]
    内部主视图: X_dtnf [D, T, N, F] (zero-copy view via moveaxis)
    
    参数:
        X_fdtN: 输入数组，形状为 [F, D, T, N]
            - F: 特征数（包含标签和可选权重）
            - D: 天数
            - T: 每天分钟数（固定 241）
            - N: 股票数（固定 6000，用NaN占位）
        assume_nan_invalid: 是否将NaN视为无效数据
    """
    
    def __init__(self, X_fdtN: np.ndarray, assume_nan_invalid: bool = True):
        assert X_fdtN.ndim == 4, "Expect [F, D, T, N]"
        self.X_dtnf = np.moveaxis(X_fdtN, (0,1,2,3), (3,0,1,2))  # -> [D, T, N, F]
        self.D, self.T, self.N, self.F = self.X_dtnf.shape
        self.assume_nan_invalid = assume_nan_invalid
        
        # 整天有效（若输入还有 NaN，占位将被排除）
        self.valid_dn = np.load('/disk4/shared/intern/laiyc/forModel/valid_dn.npy')
        self.valid_lists: List[np.ndarray] = [
            np.nonzero(self.valid_dn[d])[0] for d in range(self.valid_dn.shape[0])
        ]

    @staticmethod
    def from_fdtN(X_fdtN: np.ndarray, assume_nan_invalid: bool = True) -> "MinuteCube":
        """从 [F, D, T, N] 格式创建 MinuteCube"""
        return MinuteCube(X_fdtN, assume_nan_invalid=assume_nan_invalid)

    def day_slice(self, d: int) -> np.ndarray:
        """获取某天的数据切片 [T, N, F]"""
        return self.X_dtnf[d]  # [T, N, F]

    def minute_slice(self, d: int, t: int) -> np.ndarray:
        """获取某天某分钟的数据切片 [N, F]"""
        return self.X_dtnf[d, t]  # [N, F]
