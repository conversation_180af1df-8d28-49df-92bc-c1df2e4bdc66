"""
Collate 函数

提供针对变长样本的透传 collate 函数。
"""

from typing import List, Dict
import torch


def passthrough_collate_dict(batch: List[Dict[str, torch.Tensor]]) -> Dict[str, torch.Tensor]:
    """
    针对变长样本的透传 collate（dict 版）
    batch: List[Dict[str, Tensor or None]], len == 1
    直接返回第一个（也是唯一一个）样本
    """
    assert len(batch) == 1, f"Expected batch size 1 for passthrough collate, got {len(batch)}"
    return batch[0]
