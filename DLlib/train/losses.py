"""
可插拔损失函数

提供各种损失函数的统一接口，支持向量化计算和权重。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional

class VectorLoss(nn.Module):
    def __init__(self, loss_type: str, pred_dim, label_dim) -> None:
        super().__init__()
        self.func = getattr(VectorLoss, loss_type)
        self.pred_dim = pred_dim
        self.label_dim = label_dim

    @staticmethod
    def mse(pred, label):
        loss = F.mse_loss(pred, label, reduction="none")
        return loss

    @staticmethod
    def rmse(pred, label):
        loss = F.mse_loss(pred, label, reduction="none")
        return loss**0.5

    @staticmethod
    def mae(pred, label):
        l1 = nn.L1Loss(reduction="none")
        loss = l1(
            pred,
            label,
        )
        return loss

    @staticmethod
    def huber(pred, label, delta=1):
        loss = F.huber_loss(pred, label, delta=delta, reduction="none")
        return loss

    @staticmethod
    def bce(pred, label):
        binary = nn.BCELoss(reduction="none")
        loss = binary(
            pred,
            label,
        )
        return loss

    @staticmethod
    def cross_entropy(pred, label):
        cel = nn.CrossEntropyLoss(reduction="none")
        loss = cel(
            pred,
            (label.reshape(-1) - label.reshape(-1).min()).long(),
        )
        return loss

    @staticmethod
    def mae_withoutSig(pred, label):
        # 不用 Sigmoid 的 MAE
        epsilon = 1e-8
        pred = torch.clamp(pred, epsilon, 1 - epsilon)
        label = torch.clamp(label, epsilon, 1 - epsilon)
        pred_withoutSig = -torch.log((1 / pred) - 1)
        label_withoutSig = -torch.log((1 / label) - 1)
        loss = VectorLoss.mae(pred_withoutSig, label_withoutSig)
        return loss

    @staticmethod
    def ic(pred, label):
        tensor = torch.cat([pred.reshape(1, -1), label.reshape(1, -1)], dim=0)
        ic_loss = 1 - torch.corrcoef(tensor)[0, 1]  # 1->0
        return ic_loss

    # @staticmethod
    # def rankic(pred, label):
    #     label = label.reshape(1, -1)
    #     pred = pred.reshape(1, -1)
    #     pred = torchsort.soft_rank(pred)
    #     label = torchsort.soft_rank(label)
    #     pred = pred - pred.mean()
    #     pred = pred / pred.norm()
    #     label = label - label.mean()
    #     label = label / label.norm()
    #     return -(pred * label).sum()

    @staticmethod
    def ccc(pred, label):
        mean_x = torch.mean(pred)
        mean_y = torch.mean(label)
        std_x = torch.std(pred, unbiased=False)
        std_y = torch.std(label, unbiased=False)
        # 皮尔逊相关系数
        cov_xy = torch.mean((pred - mean_x) * (label - mean_y))
        pearson_corr = cov_xy / (std_x * std_y)
        # CCC计算
        ccc = (2 * pearson_corr * std_x * std_y) / (std_x**2 + std_y**2 + (mean_x - mean_y) ** 2)
        ccc_loss = 1 - ccc
        return ccc_loss

    @staticmethod
    def cosine(pred, label):
        cos_sim = F.cosine_similarity(pred, label)
        loss = 1 - cos_sim
        return loss

    @staticmethod
    def sharpe(pred, label):
        # rolling_pred_max = np.max(pred_calib_list[-rolling_win:])
        # weight = pred_calibrate / (1.2 * rolling_pred_max)
        # pred_max = torch.max(pred)
        # weight = pred / (1.2 * pred_max)
        # return -torch.sum(weight * label)
        pred = pred - torch.mean(pred)
        weight = (torch.sigmoid(pred) - 0.5) * 2
        print(weight)
        ret_by_position = weight * label
        ret_mean = torch.mean(ret_by_position)
        ret_vol = torch.sqrt(torch.mean(ret_by_position**2) - ret_mean**2) + 1e-8
        sharpe = (ret_mean * torch.sqrt(torch.tensor(252.0, dtype=torch.float32))) / ret_vol
        return -sharpe

    def forward(self, pred, label, weights=None):
        label = label.reshape(-1, self.label_dim)
        pred = pred.reshape(-1, self.pred_dim)
        mask = torch.any(torch.isfinite(label), dim=1)
        loss = self.func(pred[mask], label[mask])
        if weights is not None and loss.dim() > 1:
            weights = weights.reshape(-1, 1)
            loss *= weights[mask]
        return loss.mean()


class MultiClassFocalLoss(nn.Module):
    """多分类Focal损失"""
    
    def __init__(self, gamma=2, alpha=None):
        super().__init__()
        self.gamma = gamma
        if alpha is not None:
            self.alpha = torch.tensor(alpha)
        else:
            self.alpha = None

    def forward(self, inputs, targets):
        """
        前向传播
        
        参数:
            inputs: [batch_size, output_size] 已做softmax
            targets: [batch_size, 1]
        
        返回:
            focal loss
        """
        output_size = inputs.shape[-1]
        targets = (targets - targets.min()).long()
        targets_one_hot = F.one_hot(targets, num_classes=output_size).float()
        
        ce_loss = F.binary_cross_entropy(inputs, targets_one_hot, reduction="none")
        p_t = torch.exp(-ce_loss)
        modulating_factor = (1 - p_t) ** self.gamma  # 概率越大（预测越准），对Loss贡献越小
        
        # 应用类别权重
        if self.alpha is not None:
            if self.alpha.device != inputs.device:
                self.alpha = self.alpha.to(inputs.device)
            modulating_factor = modulating_factor * self.alpha[targets].reshape(modulating_factor.shape[0], 1)
        
        focal_loss = (ce_loss * modulating_factor).mean()
        return focal_loss
