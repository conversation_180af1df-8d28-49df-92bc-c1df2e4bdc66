"""
WandbTracker - Weights & Biases 实验跟踪器

仅在主进程(rank0)中初始化和记录，其他进程静默。
"""

from __future__ import annotations
import os
import time
from typing import Any, Dict, Optional, List
import torch.distributed as dist
from loguru import logger

try:
    import wandb
    WANDB_AVAILABLE = True
except ImportError:
    WANDB_AVAILABLE = False
    logger.warning("wandb not available, WandbTracker will be disabled")


def is_main_process() -> bool:
    """判断是否为主进程"""
    return (not dist.is_available()) or (not dist.is_initialized()) or dist.get_rank() == 0


class WandbTracker:
    """
    Weights & Biases 实验跟踪器
    
    特点：
    - 必须依赖 wandb
    - 仅在 rank0 初始化与记录，其它 rank 静默
    - 支持在线/离线模式
    - 自动记录模型和预测结果
    - 支持滚动训练的fold管理
    """
    
    def __init__(
        self,
        project: str,
        run_name: str,
        *,
        group: Optional[str] = None,
        job_type: Optional[str] = None,
        tags: Optional[List[str]] = None,
        mode: str = "online",     # 'online' | 'offline'
        dir_: Optional[str] = None,
        config: Optional[Dict[str, Any]] = None,
    ):
        """
        初始化 WandbTracker
        
        参数:
            project: wandb项目名称
            run_name: 实验运行名称
            group: 实验组名称
            job_type: 任务类型
            tags: 标签列表
            mode: 模式，'online'或'offline'
            dir_: wandb日志目录
            config: 配置字典
        """
        self.enabled = is_main_process() and WANDB_AVAILABLE
        self.run = None
        
        if not self.enabled:
            return
            
        # 设置环境变量
        if mode == "offline":
            os.environ["WANDB_MODE"] = "offline"
            
        # 创建目录
        if dir_:
            os.makedirs(dir_, exist_ok=True)
            
        # 初始化wandb
        self.run = wandb.init(
            project=project,
            name=run_name,
            group=group,
            job_type=job_type,
            tags=tags,
            dir=dir_,
            config=config or {},
            reinit=True,
        )
        
        if self.run:
            logger.info(f"[W&B] 初始化完成: {self.run.name} ({self.run.id})")

    def log(self, metrics: Dict[str, float], step: Optional[int] = None):
        """记录指标 - 不再使用step参数"""
        if self.enabled and self.run is not None:
            wandb.log(metrics)

    def log_metrics(self, metrics: Dict[str, float]):
        """记录指标 - 简化版本，不使用step"""
        if self.enabled and self.run is not None:
            wandb.log(metrics)

    def watch(self, model, log: str = "gradients", log_freq: int = 200):
        """监控模型"""
        if self.enabled and self.run is not None:
            wandb.watch(model, log=log, log_freq=log_freq)

    def save_artifact(self, path: str, name: Optional[str] = None, type_: str = "file"):
        """保存文件为artifact"""
        if self.enabled and self.run is not None and os.path.isfile(path):
            art = wandb.Artifact(name or os.path.basename(path), type=type_)
            art.add_file(path)
            self.run.log_artifact(art)
            logger.info(f"[W&B] 保存artifact: {path}")

    def save_checkpoint(self, checkpoint_path: str):
        """保存模型检查点"""
        self.save_artifact(checkpoint_path, type_="model")

    def log_fold_summary(self, fold_id: int, metrics: Dict[str, float]):
        """记录fold汇总信息 - 简化版本"""
        if self.enabled and self.run is not None:
            # 直接记录fold结果作为独立metrics
            fold_metrics = {f"fold_{fold_id}_{k}": v for k, v in metrics.items()}
            wandb.log(fold_metrics)
            logger.info(f"[W&B] 记录Fold {fold_id}汇总: {fold_metrics}")

    def finish(self):
        """结束实验"""
        if self.enabled and self.run is not None:
            self.run.finish()
            logger.info("[W&B] 实验结束")
