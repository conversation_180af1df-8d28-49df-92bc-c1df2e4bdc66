"""
采样工具函数

提供随机天数采样等功能。
"""

import numpy as np

__all__ = ["sample_random_days"]


def sample_random_days(
    total_days: int,
    n_days: int,
    seed: int = 42
) -> np.ndarray:
    """
    从 0…(total_days-1) 中随机抽取 n_days 天，返回升序索引
    
    参数:
        total_days: 总天数
        n_days: 要抽取的天数
        seed: 随机种子
        
    返回:
        升序排列的天索引数组
    """
    rng = np.random.default_rng(seed)
    idx = rng.choice(total_days, size=min(n_days, total_days), replace=False)
    return np.sort(idx.astype(np.int64))
