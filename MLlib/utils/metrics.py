import numpy as np
from typing import Sequence, Dict, Tuple, List, Optional

# ------------------- helper -------------------
def _spearman_vec(x: np.ndarray, y: np.ndarray) -> float:
    """
    x,y: 1-D 同长向量，已确保无 NaN，len≥3
    返回近似 Spearman（两次 argsort）
    """
    r_x = x.argsort(kind="mergesort").argsort(kind="mergesort").astype(np.float64)
    r_y = y.argsort(kind="mergesort").argsort(kind="mergesort").astype(np.float64)
    r_x -= r_x.mean(); r_y -= r_y.mean()
    denom = np.sqrt((r_x**2).sum() * (r_y**2).sum()) + 1e-12
    return float((r_x * r_y).sum() / denom)

# ------------------- main -------------------
def rank_ic_focus(
    preds:  np.ndarray,          # [M]
    y_true: np.ndarray,          # [M]
    d:      np.ndarray,          # [M] 天索引 (0 … D-1)
    t:      np.ndarray,          # [M] 分钟索引 (0 … T-1)
    n:      np.ndarray,          # [M] 股票索引 (0 … N-1)
    T_focus: Sequence[int] = (30,60,90,120,150,180,210),
) -> float:
    """
    批量 Spearman IC  @T_focus  (截面相关)，先对 (d,t) 写入立方体，再向量化求 corr。
    - preds/y_true 可含 NaN；会在计算前过滤。
    - 返回所有 (d,t) 截面的 IC 平均；若无有效截面返回 NaN。
    """
    assert preds.shape == y_true.shape == d.shape == t.shape == n.shape, "输入长度不一致"
    # 1) 过滤 NaN / focus
    mask = np.isfinite(preds) & np.isfinite(y_true)
    t_set = set(int(x) for x in T_focus)
    focus_mask = np.fromiter((tt in t_set for tt in t), dtype=bool, count=len(t))
    mask &= focus_mask
    if not mask.any():
        return float("nan")

    preds, y_true = preds[mask], y_true[mask]
    d, t, n       = d[mask].astype(np.int64), t[mask].astype(np.int64), n[mask].astype(np.int64)

    # 2) 预分配立方体 [D,N,K]
    D  = int(d.max()) + 1
    N  = int(n.max()) + 1
    k_map = {v:i for i,v in enumerate(T_focus)}   # t -> k
    K  = len(k_map)

    cube_pred = np.full((D, N, K), np.nan, dtype=np.float64)
    cube_true = np.full_like(cube_pred, np.nan)

    # 3) 写入 (向量化)
    k_idx = np.vectorize(k_map.get, otypes=[np.int64])(t)   # [M]
    cube_pred[d, n, k_idx] = preds
    cube_true[d, n, k_idx] = y_true

    # 4) 逐 (d,k) 截面向量化计算 Spearman
    ics: List[float] = []
    for k in range(K):
        Pk = cube_pred[:, :, k]     # [D,N]
        Tk = cube_true[:, :, k]     # [D,N]
        fin = np.isfinite(Pk) & np.isfinite(Tk)  # [D,N]
        for d_i in range(D):
            mask_dn = fin[d_i]
            if mask_dn.sum() < 3:
                continue
            ic = _spearman_vec(Pk[d_i, mask_dn], Tk[d_i, mask_dn])
            if np.isfinite(ic):
                ics.append(ic)

    return float(np.mean(ics)) if ics else float("nan")
