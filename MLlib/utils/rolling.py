# MLlib/utils/rolling.py
from __future__ import annotations
import numpy as np
from typing import List, Tuple, Optional

def rolling_splits_by_days(
    D: int,
    *,
    train_days: int,
    test_days: int,
    inner_val_ratio: float = 0.2,
    step_days: Optional[int] = None,
    drop_last_incomplete: bool = True,
) -> List[Tuple[np.ndarray, np.ndarray, np.ndarray]]:
    """
    按天数进行滚动切分
    
    参数:
        D: 总天数
        train_days: 训练天数
        test_days: 测试天数
        inner_val_ratio: 训练窗口内部验证比例
        step_days: 滚动步长，默认为test_days
        drop_last_incomplete: 是否丢弃最后不完整的窗口
        
    返回:
        List[Tuple[训练索引, 验证索引, 测试索引]]
    """
    if step_days is None:
        step_days = test_days
    
    out = []
    start = 0
    
    while True:
        tr_beg = start
        tr_end = tr_beg + train_days
        te_end = tr_end + test_days
        
        if tr_end > D:
            break
            
        if te_end > D:
            if drop_last_incomplete:
                break
            te_end = D
            
        # 构建训练窗口索引
        idx_train_full = np.arange(tr_beg, tr_end, dtype=np.int64)
        n_tr = len(idx_train_full)
        n_val = max(1, int(round(n_tr * inner_val_ratio)))
        
        # 分割训练和验证集
        idx_tr = idx_train_full[: n_tr - n_val]
        idx_va = idx_train_full[n_tr - n_val :]
        idx_te = np.arange(tr_end, te_end, dtype=np.int64)
        
        out.append((idx_tr, idx_va, idx_te))
        start += step_days
        
    return out

def rolling_splits_by_years(
    D: int,
    *,
    train_years: float,
    test_years: float,
    days_per_year: int,
    inner_val_ratio: float = 0.2,
) -> List[Tuple[np.ndarray, np.ndarray, np.ndarray]]:
    """
    按年数进行滚动切分
    
    参数:
        D: 总天数
        train_years: 训练年数
        test_years: 测试年数
        days_per_year: 每年天数
        inner_val_ratio: 训练窗口内部验证比例
        
    返回:
        List[Tuple[训练索引, 验证索引, 测试索引]]
    """
    return rolling_splits_by_days(
        D=D,
        train_days=int(round(train_years * days_per_year)),
        test_days=int(round(test_years * days_per_year)),
        inner_val_ratio=inner_val_ratio,
    )