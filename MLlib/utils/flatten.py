# MLlib/utils/flatten.py
from __future__ import annotations
import numpy as np

def _safe_std(std: np.ndarray, eps: float) -> np.ndarray:
    """安全的标准差计算，避免除零和非有限值"""
    return np.where((~np.isfinite(std)) | (std < eps), eps, std)

def zscore_y_cs(y: np.ndarray, eps: float = 1e-6) -> np.ndarray:
    """
    标签做截面 zscore（沿 N），用 nanmean/nanstd；原始 NaN 保留为 NaN。
      适配 y 为 [N, T, d_y] 或 [N, 1, d_y]
    """
    mask = ~np.isfinite(y)                              # True 表示原先是 NaN/Inf
    mu = np.nanmean(y, axis=0, keepdims=True)
    sd = np.nanstd (y, axis=0, keepdims=True)
    sd = _safe_std(sd, eps)
    out = (y - mu) / sd
    out[mask] = np.nan
    return out


def flat_to_cube(
    values: np.ndarray,        # [M]   float32  (pred or label)
    d:      np.ndarray,        # [M]   int32    (全局 day)
    t:      np.ndarray,        # [M]   int32
    n:      np.ndarray,        # [M]   int32
    idx_days: np.ndarray,      # [D_test] 对应当前 fold 的 global day array
    T: int,
    N: int,
    dtype=np.float32,
) -> np.ndarray:
    """
    将一维样本 (d,t,n) 写回立方体 [D_test,T,N]，缺失填 NaN

    参数:
        values: 预测值或标签值 [M]
        d: 全局天索引 [M]
        t: 分钟索引 [M]
        n: 股票索引 [M]
        idx_days: 当前fold的全局天索引数组 [D_test]
        T: 总分钟数
        N: 总股票数
        dtype: 输出数据类型

    返回:
        cube: 立方体数组 [D_test, T, N]，缺失位置填充NaN
    """
    D_test = len(idx_days)
    cube   = np.full((D_test, T, N), np.nan, dtype=dtype)

    # 把 global day -> 相对 [0..D_test-1]
    day2rel = -np.ones(idx_days.max() + 1, dtype=np.int32)   # 初始化 -1
    day2rel[idx_days] = np.arange(D_test, dtype=np.int32)    # 反向映射

    rel_d = day2rel[d]               # [M] 相对天

    # 过滤非法 (极少数情况下 day2rel=-1)
    mask  = rel_d >= 0
    rel_d = rel_d[mask]; tt = t[mask]; nn = n[mask]
    vals  = values[mask]

    # 写入 — 逐样本 assign; 向量化用同一行
    cube[rel_d, tt, nn] = vals
    return cube



