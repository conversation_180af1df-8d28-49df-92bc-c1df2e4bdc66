"""
MLlib - 机器学习模块

提供树模型训练、数据处理和评估工具，专为时间序列金融数据设计。
"""

from .utils.rolling import rolling_splits_by_days, rolling_splits_by_years
from .utils.flatten import zscore_y_cs, flat_to_cube
from .utils.metrics import rank_ic_focus
from .models.lgbm_trainer import train_lgbm_one_fold, optuna_search, optuna_search_global, save_params_json
from .data.tree_dataset import TreeCubeDataset

__all__ = [
    # 滚动切分
    'rolling_splits_by_days',
    'rolling_splits_by_years',

    # 数据扁平化
    'zscore_y_cs',
    'flat_to_cube',
    'TreeCubeDataset',

    # 评估指标
    'rank_ic_focus',

    # 模型训练
    'train_lgbm_one_fold',
    'optuna_search',
    'optuna_search_global',
    'save_params_json',
]