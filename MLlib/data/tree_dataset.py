"""
TreeCubeDataset - 树模型数据集类

将 [F,D,T,N] 立方体扁平化为树模型 tabular 样本，与神经网络 pipeline 对齐。
"""

import numpy as np
from typing import Dict, Optional, Sequence, Tuple
from DLlib.data.cube import MinuteCube

__all__ = ["TreeCubeDataset"]


def _safe_std(std: np.ndarray, eps: float = 1e-6) -> np.ndarray:
    """安全的标准差计算，避免除零和非有限值"""
    return np.where((~np.isfinite(std)) | (std < eps), eps, std)


def _zscore_cs_keep_nan(y: np.ndarray, eps: float = 1e-6) -> np.ndarray:
    """
    标签做截面 zscore（沿 N），忽略 NaN/Inf；原始 NaN/Inf 保留为 NaN。
      适配 y 为 [N, T, d_y] 或 [N, 1, d_y]

    说明：
      - 避免在“某些 (t, ch) 全为 NaN/Inf”的情况下触发 nanmean/nanstd 的空切片告警，
        因此采用手写 sum/count 与 sumsq 方案计算均值与标准差。
    """
    # 记录原始的非有限值位置，最后写回 NaN
    bad_mask = ~np.isfinite(y)  # True 表示原先是 NaN/Inf

    # 使用 float64 提升稳定性
    y64 = y.astype(np.float64, copy=False)
    fin_mask = np.isfinite(y64)

    # 统计每个 (t, ch) 的有效样本数 [1, T, d_y]
    cnt = fin_mask.sum(axis=0, keepdims=True)
    safe_cnt = np.where(cnt == 0, 1, cnt)  # 防止除 0；对应位置稍后会被 bad_mask 覆盖为 NaN

    # 有效值求和 / 求平方和
    sum_ = np.where(fin_mask, y64, 0.0).sum(axis=0, keepdims=True)
    sumsq = np.where(fin_mask, y64 * y64, 0.0).sum(axis=0, keepdims=True)

    # 均值与方差（数值稳定处理）
    mu = sum_ / safe_cnt
    mean_sq = sumsq / safe_cnt
    var = mean_sq - mu * mu
    var = np.where(var < 0.0, 0.0, var)  # 数值误差下限截断
    sd = np.sqrt(var)

    # 安全下界，避免除以 0 或非有限
    sd = _safe_std(sd, eps)

    out = (y64 - mu) / sd
    out[bad_mask] = np.nan
    return out

class TreeCubeDataset:
    """
    把 [F,D,T,N] 立方体扁平化为树模型 tabular 样本

    Parameters
    ----------
    X_fdtn : np.ndarray
        原始数据 (可 mmap) 形状 [F,D,T,N]
    n_feat / n_label / n_weight : int
        通道切分规格（与 FieldSpec 保持一致）
    """

    def __init__(
        self,
        X_fdtn: np.ndarray,
        *,
        n_feat: int,
        n_label: int,
        n_weight: int = 0,
    ):
        assert X_fdtn.ndim == 4, "`X_fdtn` shape must be [F,D,T,N]"
        F, D, T, N = X_fdtn.shape
        assert n_feat + n_weight + n_label == F, "channel split mismatch"

        # 使用 MinuteCube 管理惰性视图与有效股票列表
        self.cube = MinuteCube.from_fdtN(X_fdtn)
        self.valid_dn = self.cube.valid_dn               # [D, N]
        self.valid_lists = self.cube.valid_lists         # List[np.ndarray], 每天的有效股票索引

        self.n_feat  = int(n_feat)
        self.n_label = int(n_label)
        self.n_weight = int(n_weight)

        self.F, self.D, self.T, self.N = F, D, T, N

    def flatten(
        self,
        *,
        drop_nan_y: bool = True,
        day_idx: Optional[Sequence[int]] = None,
    ) -> Dict[str, np.ndarray]:
        """
        将 (可选子集 day_idx) 扁平化为表格，假设 day_idx 连续。
        使用 MinuteCube 的 day_slice 保持惰性切片；每个 d 使用 valid_lists[d] 过滤有效股票；
        对 y 在每个 (d,t) 沿 N 做截面 zscore。
        """
        # 选择天索引（假设连续）
        if day_idx is None:
            day_idx = np.arange(self.D, dtype=np.int64)
        else:
            day_idx = np.asarray(day_idx, dtype=np.int64)
            if day_idx.size > 1:
                diff = np.diff(day_idx)
                assert np.all(diff == 1), "flatten: 要求 day_idx 连续（步长=1）"
        Dp = int(day_idx.size)
        if Dp == 0:
            return {
                "X": np.empty((0, self.n_feat), dtype=np.float32),
                "y": np.empty((0,), dtype=np.float32),
                "d": np.empty((0,), dtype=np.int32),
                "t": np.empty((0,), dtype=np.int32),
                "n": np.empty((0,), dtype=np.int32),
            }

        # 预计算每一天的有效股票数与总样本数，便于一次性预分配
        Nv = [self.valid_lists[int(d)].size for d in day_idx]
        M_total = int(self.T * np.sum(Nv))
        if M_total == 0:
            return {
                "X": np.empty((0, self.n_feat), dtype=np.float32),
                "y": np.empty((0,), dtype=np.float32),
                "d": np.empty((0,), dtype=np.int32),
                "t": np.empty((0,), dtype=np.int32),
                "n": np.empty((0,), dtype=np.int32),
            }

        X_all = np.empty((M_total, self.n_feat), dtype=np.float32)
        y_all = np.empty((M_total,), dtype=np.float32)
        d_all = np.empty((M_total,), dtype=np.int32)
        t_all = np.empty((M_total,), dtype=np.int32)
        n_all = np.empty((M_total,), dtype=np.int32)

        ptr = 0
        for d in day_idx:
            n_idx = self.valid_lists[int(d)]
            if n_idx.size == 0:
                continue
            X_TNF = self.cube.day_slice(int(d))                 # [T, N, F_total]
            X_TNF = X_TNF[:, n_idx, :]                          # [T, N_valid, F_total]
            X_NTF = np.moveaxis(X_TNF, 0, 1)                    # [N_valid, T, F_total]

            f_np = X_NTF[..., : self.n_feat]                    # [N_valid, T, F_feat]
            y_np = X_NTF[..., self.n_feat + self.n_weight : self.n_feat + self.n_weight + self.n_label]
            y_np = y_np[..., 0]                                 # [N_valid, T]

            # y 截面 zscore（沿 N）
            y_z = _zscore_cs_keep_nan(y_np)                     # [N_valid, T]

            X_flat_d = f_np.reshape(-1, self.n_feat).astype(np.float32)
            y_flat_d = y_z.reshape(-1).astype(np.float32)
            M_d = X_flat_d.shape[0]

            # 与 C-order 展平一致的 (t, n) 栅格
            t_flat_d = np.tile(np.arange(self.T, dtype=np.int32), reps=n_idx.size)
            n_flat_d = np.repeat(n_idx.astype(np.int32), repeats=self.T)
            d_flat_d = np.full((M_d,), int(d), dtype=np.int32)

            if drop_nan_y:
                mask = np.isfinite(y_flat_d)
                X_flat_d = X_flat_d[mask]
                y_flat_d = y_flat_d[mask]
                t_flat_d = t_flat_d[mask]
                n_flat_d = n_flat_d[mask]
                d_flat_d = d_flat_d[mask]
                M_d = X_flat_d.shape[0]

            X_all[ptr:ptr+M_d] = X_flat_d
            y_all[ptr:ptr+M_d] = y_flat_d
            t_all[ptr:ptr+M_d] = t_flat_d
            n_all[ptr:ptr+M_d] = n_flat_d
            d_all[ptr:ptr+M_d] = d_flat_d
            ptr += M_d

        # 截断（极端情况下若全 NaN 会出现 ptr < M_total）
        if ptr < M_total:
            X_all = X_all[:ptr]
            y_all = y_all[:ptr]
            d_all = d_all[:ptr]
            t_all = t_all[:ptr]
            n_all = n_all[:ptr]

        return {"X": X_all, "y": y_all, "d": d_all, "t": t_all, "n": n_all}

    def split_and_flatten(
        self,
        idx_tr: Sequence[int],
        idx_va: Sequence[int],
        idx_te: Sequence[int],
        *,
        drop_nan_y: bool = True,
    ) -> Tuple[Dict[str,np.ndarray], Dict[str,np.ndarray], Dict[str,np.ndarray]]:
        """传入天索引三份，一次性得到三份扁平表格"""
        return (
            self.flatten(drop_nan_y=drop_nan_y, day_idx=idx_tr),
            self.flatten(drop_nan_y=drop_nan_y, day_idx=idx_va),
            self.flatten(drop_nan_y=drop_nan_y, day_idx=idx_te),
        )
