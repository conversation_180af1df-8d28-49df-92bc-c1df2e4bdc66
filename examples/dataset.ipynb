{"cells": [{"cell_type": "code", "execution_count": null, "id": "9608a51b", "metadata": {}, "outputs": [], "source": ["%cd ../"]}, {"cell_type": "code", "execution_count": null, "id": "12148f0c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 数据集使用示例 ===\n", "\n", "1. 加载和准备数据...\n", "原始数据形状: (8, 61, 241, 6000)\n", "Cube: D=61, T=241, N=6000, F=8\n", "训练集天数: 48, 验证集天数: 13\n", "\n", "2. 数据标准化...\n", "标准化完成\n", "\n", "3. 定义通道规格...\n", "通道规格: 7特征 + 0权重 + 1标签\n", "\n", "4. 创建数据集...\n", "DaySection 数据集长度: 48\n", "DaySection 样本 - 特征: torch.<PERSON><PERSON>([4271, 241, 7]), 标签: torch.<PERSON><PERSON>([4271, 241, 1]), 权重: torch.<PERSON><PERSON>([4271, 241, 1])\n", "Pair 数据集长度: 211338\n", "Pair 样本 - 特征: torch.<PERSON><PERSON>([241, 7]), 标签: torch.<PERSON><PERSON>([241, 1]), 权重: torch.<PERSON><PERSON>([241, 1])\n", "分钟回看数据集长度: 11568\n", "分钟回看样本 - 特征: torch.<PERSON><PERSON>([4271, 16, 7]), 标签: torch.<PERSON><PERSON>([4271, 16, 1]), 权重: torch.<PERSON><PERSON>([4271, 16, 1])\n"]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m在当前单元格或上一个单元格中执行代码时 Kernel 崩溃。\n", "\u001b[1;31m请查看单元格中的代码，以确定故障的可能原因。\n", "\u001b[1;31m单击<a href='https://aka.ms/vscodeJupyterKernelCrash'>此处</a>了解详细信息。\n", "\u001b[1;31m有关更多详细信息，请查看 Jupyter <a href='command:jupyter.viewOutput'>log</a>。"]}], "source": ["\"\"\"\n", "简单使用示例\n", "\n", "展示如何使用新的数据集类进行基本的数据加载和处理。\n", "\"\"\"\n", "\n", "import numpy as np\n", "import torch\n", "from torch.utils.data import DataLoader\n", "from torch.utils.data.distributed import DistributedSampler\n", "import sys\n", "import os\n", "\n", "\n", "from DLlib.data import (\n", "    FieldSpec,\n", "    MinuteCube,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    TensorDaySectionDataset,\n", "    TensorPairDataset,\n", "    TensorMinuteLookbackWithinDayDataset,\n", "    TensorMinuteLookbackAcrossDaysDataset,\n", "    DistributedSingleIndexBatchSampler,\n", "    passthrough_collate_dict\n", ")\n", "\n", "\n", "\"\"\"主函数：展示基本用法\"\"\"\n", "print(\"=== 数据集使用示例 ===\")\n", "\n", "# === 1. 数据准备 ===\n", "print(\"\\n1. 加载和准备数据...\")\n", "X = np.load(\"projs/stock1m/data/OHLCVA_Vwap_cube.npy\")  # [F=8, D=61, T=241, N=6000]\n", "print(f\"原始数据形状: {X.shape}\")\n", "\n", "# 创建 MinuteCube\n", "cube_all = MinuteCube.from_fdtN(X)\n", "print(f\"Cube: D={cube_all.D}, T={cube_all.T}, N={cube_all.N}, F={cube_all.F}\")\n", "\n", "# 拆分训练/验证集\n", "cube_train, cube_valid, _, _ = cube_all.split_days(train_ratio=0.8)\n", "print(f\"训练集天数: {cube_train.D}, 验证集天数: {cube_valid.D}\")\n", "\n", "# === 2. 标准化 ===\n", "print(\"\\n2. 数据标准化...\")\n", "spec = FieldSpec(n_feat=7, n_label=1, n_weight=0)\n", "#std = CubeStandardizer(mode=\"per_stock_f\", eps=1e-6)\n", "#std.fit(cube_train, spec=spec)\n", "cube_train_std = cube_train\n", "cube_valid_std = cube_valid\n", "print(\"标准化完成\")\n", "\n", "# === 3. 定义通道规格 ===\n", "print(\"\\n3. 定义通道规格...\")\n", "# 7个特征 + 1个标签（无权重）\n", "\n", "print(f\"通道规格: {spec.n_feat}特征 + {spec.n_weight}权重 + {spec.n_label}标签\")\n", "\n", "# === 4. 创建不同类型的数据集 ===\n", "print(\"\\n4. 创建数据集...\")\n", "\n", "# 4.1 DaySection 数据集（变长样本）\n", "ds_day = TensorDaySectionDataset(cube_train_std, spec)\n", "print(f\"DaySection 数据集长度: {len(ds_day)}\")\n", "\n", "sample_day = ds_day[0]\n", "print(f\"DaySection 样本 - 特征: {sample_day['features'].shape}, 标签: {sample_day['label'].shape}, 权重: {sample_day['weight'].shape}\")\n", "\n", "# 4.2 Pair 数据集（固定形状）\n", "ds_pair = TensorPairDataset(cube_train_std, spec)\n", "print(f\"Pair 数据集长度: {len(ds_pair)}\")\n", "\n", "sample_pair = ds_pair[0]\n", "print(f\"Pair 样本 - 特征: {sample_pair['features'].shape}, 标签: {sample_pair['label'].shape}, 权重: {sample_pair['weight'].shape}\")\n", "\n", "# 4.3 分钟回看数据集\n", "ds_lookback = TensorMinuteLookbackAcrossDaysDataset(cube_train_std, spec, L=16, filter_mode='window')\n", "print(f\"分钟回看数据集长度: {len(ds_lookback)}\")\n", "\n", "sample_lookback = ds_lookback[0]\n", "print(f\"分钟回看样本 - 特征: {sample_lookback['features'].shape}, 标签: {sample_lookback['label'].shape}, 权重: {sample_lookback['weight'].shape}\")\n", "\n", "\n", "# 5.1 变长样本：使用自定义分布式采样器\n", "sampler_day = DistributedSingleIndexBatchSampler(\n", "    num_samples=len(ds_day),\n", "    num_replicas=1,  # 单进程模拟\n", "    rank=0,\n", "    shuffle=False\n", ")\n", "\n", "loader_day = DataLoader(\n", "    ds_day,\n", "    batch_sampler=sampler_day,\n", "    collate_fn=passthrough_collate_dict,\n", "    num_workers=0  # 简化示例\n", ")\n", "\n", "# 5.2 固定形状：使用普通 DataLoader\n", "loader_pair = DataLoader(\n", "    ds_pair,\n", "    batch_size=32,\n", "    shuffle=False,\n", "    num_workers=0\n", ")\n", "\n", "sample_lookback = DistributedSingleIndexBatchSampler(\n", "    num_samples=len(ds_lookback),\n", "    num_replicas=1,  # 单进程模拟\n", "    rank=0,\n", "    shuffle=False\n", ")\n", "\n", "loader_lookback = DataLoader(\n", "    ds_lookback,\n", "    batch_sampler=sample_lookback,\n", "    collate_fn=passthrough_collate_dict,\n", "    num_workers=0  # 简化示例\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "id": "83a38622", "metadata": {}, "outputs": [], "source": ["# === 6. 数据迭代示例 ===\n", "print(\"\\n6. 数据迭代示例...\")\n", "\n", "print(\"DaySection DataLoader:\")\n", "for i, batch in enumerate(loader_day):\n", "    print(f\"  批次 {i}: 特征 {batch['features'].shape}, 标签 {batch['label'].shape}\")\n", "    print(batch['features'][0])\n", "    print(batch['label'][0])\n", "    break\n", "    # assert features no any nan\n", "    assert batch['features'].isnan().sum() == 0\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "2700b75a", "metadata": {}, "outputs": [], "source": ["\n", "see_f, see_label = None, None\n", "print(\"\\nLookback DataLoader:\")\n", "for i, batch in enumerate(loader_lookback):\n", "    print(f\"  批次 {i}: 特征 {batch['features'].shape}, 标签 {batch['label'].shape}\")\n", "    assert batch['features'].isnan().sum() == 0\n", "\n", "\n", "\n", "\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "dl", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}