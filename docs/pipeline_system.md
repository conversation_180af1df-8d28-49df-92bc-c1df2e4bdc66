# TSLib Pipeline 系统

## 概述

TSLib Pipeline 系统是一个完整的深度学习训练框架，专为股票分钟级数据设计，支持多种数据集类型、可插拔损失函数、分布式训练和实验跟踪。

## 核心特性

### 🚀 三种Pipeline类型
1. **DaySection Pipeline** - 全截面分析
   - 适用场景：因子模型、排序模型、截面分析
   - 数据格式：`[N_valid, T, F]` → `[N_valid, T, 1]`
   - 支持截面Attention（无需mask）

2. **Pair Pipeline** - 个股时间序列分析
   - 适用场景：LSTM、Transformer、个股预测
   - 数据格式：`[B, T, F]` → `[B, T, 1]`
   - 固定形状，可正常batch

3. **MinuteAcross Pipeline** - 跨天回看预测
   - 适用场景：长期依赖建模、连续预测
   - 数据格式：`[N_sel, L, F]` → `[N_sel, 1]`
   - 支持全局操作，无需mask

### 🔧 可插拔损失函数
- **VectorLoss**: 支持多种损失类型
  - `rmse`, `mae`, `mse`, `huber`
  - `ic`, `ccc`, `cosine`, `sharpe`
  - `bce`, `cross_entropy`
- **MultiClassFocalLoss**: 多分类Focal损失
- 自动处理NaN值和权重

### 📊 实验跟踪
- **WandB集成**: 自动记录训练过程
  - 在线/离线模式
  - 自动保存模型和预测结果
  - 支持分布式训练（仅rank0记录）
- **滚动训练**: 支持时间序列交叉验证
  - 可配置训练/测试窗口
  - 自动生成多个fold
  - 每个fold独立训练和评估

### 🖥️ 分布式训练
- **DDP支持**: 多卡并行训练
- **自动同步**: 梯度规约和指标聚合
- **早停机制**: 基于验证集指标
- **资源管理**: 自动GPU内存清理

## 使用方法

### 1. 单卡测试
```bash
# 测试单个fold
python projs/stock1m/scripts/test_single_fold.py

# 测试完整pipeline
python projs/stock1m/scripts/run_pipeline.py --pipeline day --dry_run
```

### 2. 多卡DDP训练
```bash
# 使用启动脚本
bash projs/stock1m/scripts/run_ddp_pipeline.sh --pipeline day --nproc 8

# 或直接使用torchrun
torchrun --nproc_per_node=8 projs/stock1m/scripts/run_pipeline.py --pipeline day
```

### 3. 配置选项
```bash
# 选择不同的pipeline
--pipeline day      # DaySection Pipeline
--pipeline pair     # Pair Pipeline  
--pipeline minute   # MinuteAcross Pipeline

# 指定数据路径
--data_path /path/to/data.npy

# 设置GPU数量
--nproc 8
```

## 配置参数

### 数据配置
- `data_path`: 数据文件路径
- `n_feat`: 特征维度 (默认: 7)
- `n_label`: 标签维度 (默认: 1)
- `n_weight`: 权重维度 (默认: 0)

### 滚动配置
- `train_years`: 训练窗口年数 (默认: 3.0)
- `test_years`: 测试窗口年数 (默认: 1.0)
- `days_per_year`: 每年天数 (默认: 2，适配61天小样本)
- `inner_val_ratio`: 验证集比例 (默认: 0.2)

### 模型配置
- `hidden`: 隐藏层维度 (默认: 128)
- `layers`: 网络层数 (默认: 1)
- `dropout`: Dropout比例 (默认: 0.1)

### 训练配置
- `lr`: 学习率 (默认: 1e-4)
- `weight_decay`: 权重衰减 (默认: 1e-4)
- `epochs`: 训练轮数 (默认: 20)
- `early_stop`: 早停容忍轮数 (默认: 10)

### 损失和指标配置
- `loss_type`: 损失函数类型 (默认: "rmse")
- `primary_metric`: 主要指标 (默认: "rmse")
- `primary_higher_better`: 主要指标是否越高越好 (默认: False)

## 日志格式

系统使用彩色emoji日志，便于区分不同阶段：

```
🟢 [Epoch 1] Train: loss=0.742385 | rmse=0.907556 | ic=0.565430
🔵    [Epoch 1] Valid: loss=0.826886 | rmse=1.144479 | ic=0.296873
```

- **🟢 绿色圆圈**: 训练阶段
- **🔵 蓝色圆圈**: 验证阶段（有缩进）
- **📊 📋 🔧 🤖**: 不同操作阶段的标识

## 输出文件

### 模型文件
```
projs/stock1m/checkpoints/
├── day_fold00/
│   ├── best.pt              # 最佳模型权重
│   ├── test_preds.npy       # 测试集预测
│   └── test_labels.npy      # 测试集标签
├── day_fold01/
│   └── ...
```

### WandB记录
- 训练/验证指标曲线
- 模型权重artifacts
- 预测结果artifacts
- 超参数配置

## 扩展指南

### 添加新的损失函数
在 `DLlib/train/losses.py` 的 `VectorLoss` 类中添加静态方法：

```python
@staticmethod
def new_loss(pred, label):
    # 实现新的损失函数
    return loss_value
```

### 添加新的评估指标
在 `DLlib/train/metrics.py` 中添加新函数：

```python
def new_metric(pred, target, weight=None):
    # 实现新的评估指标
    return metric_value
```

### 创建新的Pipeline
参考现有pipeline结构，创建新的 `.py` 文件并实现 `run()` 函数。

## 测试结果

### 单卡测试
- ✅ 数据加载和预处理
- ✅ 模型训练和验证
- ✅ WandB在线/离线模式
- ✅ 早停机制
- ✅ 预测结果保存

### 多卡DDP测试
- ✅ 2卡分布式训练
- ✅ 梯度同步和指标聚合
- ✅ 仅rank0记录日志和保存
- ✅ 3个fold完整流程

### 性能表现
- 训练速度：~1-2秒/epoch (小模型)
- 内存使用：合理，无内存泄漏
- GPU利用率：良好的多卡负载均衡

## 环境要求

- Python 3.8+
- PyTorch 2.0+
- CUDA 11.8+
- wandb
- loguru
- numpy

## 下一步计划

- [ ] 更多模型架构（Transformer、CNN）
- [ ] 更多评估指标（MAE、Huber、CCC等）
- [ ] 超参数优化集成
- [ ] 模型解释性分析
- [ ] 生产环境部署支持
