# TSLib 项目架构文档

## 项目概述

TSLib 是一个专为股票分钟级数据设计的深度学习框架，提供从数据处理到模型训练的完整流水线，支持多卡 DDP 分布式训练。

## 核心设计理念

### 1. 数据优先设计
- **统一数据格式**: 所有数据以 `[F, D, T, N]` 格式存储（特征、天数、分钟、股票）
- **零拷贝视图**: 通过 `np.moveaxis` 实现高效的数据视图转换
- **有效性过滤**: 自动处理 NaN 占位和股票退市情况

### 2. 模块化架构
- **数据层**: 负责数据加载、预处理和标准化
- **训练层**: 提供统一的训练接口和评估指标
- **模型层**: 包含各种神经网络架构
- **项目层**: 具体的应用实例和配置

## 目录结构

```
tslib/
├── DLlib/                              # 核心深度学习框架
│   ├── data/                           # 数据处理模块
│   │   ├── __init__.py                 # 模块导出
│   │   ├── cube.py                     # MinuteCube 数据容器
│   │   ├── field_spec.py               # FieldSpec 通道拆分规格
│   │   ├── standardizer.py             # CubeStandardizer 标准化器
│   │   ├── datasets.py                 # 四种 Tensor 数据集类
│   │   ├── samplers.py                 # 分布式采样器
│   │   ├── collate.py                  # 透传 collate 函数
│   │   └── ddp_utils.py                # DDP 工具函数
│   ├── train/                          # 训练模块
│   │   ├── __init__.py                 # 模块导出
│   │   ├── trainer.py                  # ModelTrainer 训练器
│   │   └── metrics.py                  # 评估指标 (RMSE, IC)
│   ├── models/                         # 模型架构
│   │   ├── __init__.py                 # 模块导出
│   │   └── seq_models.py               # 序列模型 (GRU)
│   ├── layers/                         # 自定义层 (预留)
│   └── optimizer/                      # 优化器 (预留)
│       ├── famo.py                     # FAMO 优化器
│       └── min_norm_solvers.py         # 最小范数求解器
├── MLlib/                              # 传统机器学习库 (预留)
│   ├── models/                         # 传统模型
│   └── utils/                          # 工具函数
├── projs/                              # 项目实例
│   ├── stock1m/                        # 股票1分钟数据项目
│   │   ├── data/                       # 数据文件
│   │   │   └── OHLCVA_Vwap_cube.npy    # 股票数据 [F=8,D=61,T=241,N=6000]
│   │   ├── scripts/                    # 示例脚本
│   │   │   ├── ddp_day_section.py      # DaySectionDataset DDP 示例
│   │   │   ├── ddp_pair.py             # PairDataset DDP 示例
│   │   │   ├── ddp_minute_within_day.py # MinuteLookbackWithinDay 示例
│   │   │   ├── ddp_minute_across_days.py # MinuteLookbackAcrossDay 示例
│   │   │   └── run_all_ddp_examples.sh # 运行所有示例
│   │   ├── checkpoints/                # 模型检查点
│   │   ├── logs/                       # 训练日志
│   │   ├── main_fast.py                # 快速训练脚本
│   │   ├── main_test.py                # 测试训练脚本
│   │   └── run_training.sh             # 训练启动脚本
│   └── crypto15m/                      # 加密货币15分钟数据项目 (预留)
├── examples/                           # 示例代码
│   ├── simple_usage_example.py         # 基础使用示例
│   └── dataset.ipynb                   # 数据集使用教程
├── scripts/                            # 工具脚本
│   └── test_ddp.sh                     # DDP 测试脚本
├── docs/                               # 文档
│   └── architecture.md                 # 架构文档
└── cubesave.ipynb                      # 数据保存笔记本
```

## 核心组件

### DLlib.data - 数据处理模块

#### 核心类
- **MinuteCube**: 数据容器，管理 `[F,D,T,N]` 到 `[D,T,N,F]` 的视图转换
- **FieldSpec**: 通道拆分规格，定义特征/权重/标签的排列
- **CubeStandardizer**: 数据标准化器，支持全局和按股票标准化

#### 四种数据集类
1. **TensorDaySectionDataset** - 全截面分析
   - 输出: `[N_valid, T, F]`
   - 适用: 因子模型、排序模型、截面分析
   - 特点: 变长样本，支持截面 Attention

2. **TensorPairDataset** - 个股时间序列
   - 输出: `[B, T, F]`
   - 适用: LSTM、Transformer、个股预测
   - 特点: 固定形状，可正常 batch

3. **TensorMinuteLookbackWithinDayDataset** - 日内回看
   - 输出: `[N_valid, L, F]`
   - 适用: 高频交易、日内预测
   - 特点: 不跨天，避免隔夜跳空

4. **TensorMinuteLookbackAcrossDaysDataset** - 跨天回看
   - 输出: `[N_sel, L, F]`
   - 适用: 长期依赖建模、连续预测
   - 特点: 可跨天，支持全局操作

#### 分布式支持
- **DistributedSingleIndexBatchSampler**: 变长样本的分布式采样器
- **官方 DistributedSampler**: 固定形状样本使用
- **passthrough_collate_dict**: 变长样本的透传 collate 函数

### DLlib.train - 训练模块

#### 核心组件
- **ModelTrainer**: 统一训练器，支持 DDP 和早停
- **EarlyStopper**: 早停机制
- **指标函数**: RMSE、IC 等评估指标

#### 特点
- 可插拔的损失函数和评估指标
- 自动 DDP 同步和规约
- 支持梯度累积和裁剪
- 主进程保存和日志记录

### DLlib.models - 模型模块

#### 序列模型
- **GRUSeq**: 序列到序列模型，适用于 DaySection/Pair
- **GRUPoint**: 点预测模型，适用于 Lookback 数据集

## 数据流设计

### 1. 数据加载流程
```
原始数据 [F,D,T,N] 
    ↓
MinuteCube (视图转换 + 有效性过滤)
    ↓
CubeStandardizer (标准化)
    ↓
TensorDataset (张量化 + 通道拆分)
    ↓
DataLoader (分布式采样)
```

### 2. 训练流程
```
DataLoader
    ↓
ModelTrainer (前向传播 + 损失计算)
    ↓
DDP 同步 (梯度规约)
    ↓
指标计算 (RMSE, IC 等)
    ↓
早停检查 + 模型保存
```

## 关键设计决策

### 1. 权重处理
- 无权重时返回全1张量，而非 None
- 保证 DataLoader 的 collate 函数正常工作
- 统一的字典格式: `{"features", "label", "weight"}`

### 2. Attention 设计考虑
- **时序 Attention**: 需要 mask，防止未来信息泄露
- **截面 Attention**: DaySection 可以不用 mask
- **全局 Attention**: Lookback 数据集支持任意操作

### 3. DDP 兼容性
- 变长样本使用自定义 batch sampler
- 固定形状样本使用官方 sampler
- 自动处理 epoch 随机种子同步

## 使用示例

### 基本训练流程
```python
# 数据准备
cube = MinuteCube.from_fdtN(X)
cube_train, cube_valid = cube.split_days(0.8)
std = CubeStandardizer().fit(cube_train)
cube_train_std = std.transform(cube_train)

# 数据集创建
spec = FieldSpec(n_feat=7, n_label=1, n_weight=0)
dataset = TensorDaySectionDataset(cube_train_std, spec)

# 训练器
trainer = ModelTrainer(model, optimizer, loss_fn=rmse, metrics={"ic": ic_cs})
trainer.fit(train_loader, valid_loader, num_epochs=20, save_path="model.pt")
```

### 启动分布式训练
```bash
torchrun --nproc_per_node=8 projs/stock1m/main.py
```

## 扩展性设计

### 1. 新数据类型
- 继承 MinuteCube 设计模式
- 实现对应的 Dataset 类
- 配置 FieldSpec 规格

### 2. 新模型架构
- 实现标准的 PyTorch nn.Module
- 注意输入输出形状匹配
- 考虑 Attention mask 需求

### 3. 新评估指标
- 在 metrics.py 中添加函数
- 支持 DDP 规约
- 可作为损失函数使用

## 性能特点

- **内存效率**: 零拷贝视图转换，避免数据复制
- **计算效率**: 8卡 DDP 并行，数据传输带宽 5-22 GB/s
- **存储效率**: 支持 memmap 大文件处理
- **训练效率**: 自动早停，避免过拟合

## 项目状态

当前项目专注于 DaySectionDataset 模式的全截面分析，已完成：
- ✅ 完整的数据处理流水线
- ✅ 8卡 DDP 分布式训练
- ✅ 基础的 GRU 模型架构
- ✅ RMSE 和 IC 评估指标

下一步计划：
- 🔄 更复杂的模型架构（Attention 机制）
- 🔄 更多评估指标（MAE, Huber, CCC 等）
- 🔄 其他三种 Dataset 的应用场景
